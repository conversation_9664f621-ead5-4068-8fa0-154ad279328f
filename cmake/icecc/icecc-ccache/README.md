# 分布式构建测试项目

## 项目概述

这是一个专门用于验证icecream分布式构建系统是否正常工作的C++测试项目。项目包含多个模块和足够的编译任务，能够有效展示分布式编译的效果。

## 项目结构

```
test-project/
├── CMakeLists.txt          # CMake构建配置
├── main.cpp                # 主程序入口
├── test_lib_main.cpp       # 静态库测试程序
├── module1.h/cpp           # 数学计算模块
├── module2.h/cpp           # 字符串处理模块
├── module3.h/cpp           # 文件操作模块
├── module4.h/cpp           # 网络工具模块
└── README.md               # 本文档
```

## 构建目标

项目包含以下构建目标：

1. **main** - 主测试程序，包含所有模块的功能测试
2. **test_with_lib** - 静态库链接测试程序
3. **testlib** - 静态库，包含module1和module2

## 使用方法

### 1. 启动分布式构建环境

```bash
# 在项目根目录下启动演示环境
docker-compose -f docker-compose-demo.yml up -d

# 等待环境完全启动
sleep 20
```

### 2. 进入构建客户端

```bash
# 进入构建客户端容器
docker-compose -f docker-compose-demo.yml exec build-client bash
```

### 3. 验证分布式环境

```bash
# 检查调度器连接状态
icecc --version

# 查看工作节点连接情况（在调度器容器中）
docker-compose logs icecc-scheduler | grep "login"

# 检查环境变量
echo "ICECC_SCHEDULER: $ICECC_SCHEDULER"
echo "CCACHE_PREFIX: $CCACHE_PREFIX"
```

### 4. 执行构建

```bash
# 配置构建
cmake .

# 分布式编译（推荐）
make -j8 VERBOSE=1

# 或者单机编译（对比用）
make -j$(nproc) VERBOSE=1
```

### 5. 运行测试程序

```bash
# 运行主程序
./main

# 运行静态库测试程序
./test_with_lib
```

### 6. 检查构建统计

```bash
# 查看ccache统计信息
ccache -s

# 查看icecream统计（如果可用）
icecc --version
```

## 验证分布式构建是否生效

### 关键指标

1. **编译节点连接状态**
   ```bash
   # 检查调度器日志中的节点连接
   docker-compose logs icecc-scheduler | grep "login"
   ```
   应该显示工作节点的登录信息

2. **分布式编译验证**
   使用 ICECC_DEBUG 模式查看详细的分布式编译过程：
   ```bash
   # 启用 icecc 调试模式（临时）
   export ICECC_DEBUG=1

   # 执行编译任务（仅编译，不链接）
   /usr/lib64/ccache/c++ -std=c++11 -O2 -c source.cpp -o source.o

   # 或者单次使用（推荐）
   ICECC_DEBUG=1 /usr/lib64/ccache/c++ -std=c++11 -O2 -c source.cpp -o source.o

   # 查看关键信息：
   # - "asking for host to use" - 请求远程主机
   # - "connected to 172.20.0.x" - 连接到工作节点
   # - "sent X bytes" - 发送源代码到远程节点
   # - "got X bytes" - 接收编译结果
   ```

3. **实时监控工作节点活动**
   在编译过程中监控工作节点的进程活动：
   ```bash
   # 在另一个终端中运行监控
   while true; do
     echo "=== $(date +%H:%M:%S) ==="
     docker-compose exec icecc-worker-1 ps aux | grep gcc
     docker-compose exec icecc-worker-2 ps aux | grep gcc
     sleep 1
   done
   ```

4. **构建速度对比**
   ```bash
   # 分布式构建（推荐并行数：8-16）
   time make -j8

   # 单机构建对比
   time make -j$(nproc)
   ```

4. **ccache统计**
   ```bash
   ccache -s
   ```
   查看缓存命中率和统计信息

### 预期输出

程序运行时会显示：
- 编译器版本信息
- 各模块功能测试结果
- 分布式构建环境变量
- 性能测试结果

### 分布式编译工作原理

**重要理解**：
- icecream 只对**编译步骤**（-c 参数）进行分布式处理
- **链接步骤**始终在本地执行
- 系统使用智能调度，小文件可能选择本地编译以避免网络开销
- 工作节点**不需要**预装编译环境包，客户端会自动传输

**验证要点**：
- 使用 `ICECC_DEBUG=1` 查看详细的调度过程
- 关注 "connected to 172.20.0.x" 表示连接到工作节点
- "sent X bytes" 表示源代码已发送到远程节点进行编译

## ICECC_DEBUG 调试配置

### 调试级别

ICECC_DEBUG 环境变量支持不同的调试级别：

```bash
# 基本信息级别
export ICECC_DEBUG=info

# 警告信息级别
export ICECC_DEBUG=warning

# 详细调试信息（推荐用于问题诊断）
export ICECC_DEBUG=debug

# 或者简单启用（等同于 debug）
export ICECC_DEBUG=1
```

### 使用方法

**临时启用（推荐）**：
```bash
# 单次编译使用
ICECC_DEBUG=1 make -j8

# 或者临时设置环境变量
export ICECC_DEBUG=1
make -j8
unset ICECC_DEBUG  # 使用后清除
```

**通过 docker-compose 启用**：
```yaml
# 在 docker-compose.yml 中添加
services:
  build-client:
    environment:
      - ICECC_DEBUG=1  # 仅在需要调试时添加
```

### 注意事项

- **性能影响**：调试模式会产生大量日志，影响编译性能
- **日志污染**：会在控制台输出详细的调试信息
- **生产环境**：不建议在生产环境中默认启用
- **按需使用**：建议仅在诊断问题时临时启用

## 故障排除

### 常见问题

1. **如何确定合适的并行编译数量**
   - 建议使用固定的并行数：`make -j8` 或 `make -j16`
   - 可以根据集群中工作节点数量调整
   - 通过调度器日志验证节点连接状态：
     ```bash
     docker-compose logs icecc-scheduler | grep "login"
     ```

2. **分布式编译未生效**
   - 检查 ccache 日志确认是否调用了 icecc：
     ```bash
     export CCACHE_LOGFILE=/tmp/ccache.log
     make clean && make -j8
     grep "icecc" /tmp/ccache.log
     ```
   - 验证 ICECC_VERSION 环境变量已设置
   - 检查调度器和工作节点的连接状态

3. **性能没有提升**
   - 确认有多个工作节点在运行并已连接到调度器
   - 检查项目是否足够大以体现分布式优势
   - 验证网络延迟是否过高
   - 尝试调整并行数（建议范围：4-16）

### 调试命令

```bash
# 查看容器状态
docker-compose -f docker-compose-demo.yml ps

# 查看调度器日志
docker-compose -f docker-compose-demo.yml logs icecc-scheduler

# 查看工作节点日志
docker-compose -f docker-compose-demo.yml logs icecc-worker-1
docker-compose -f docker-compose-demo.yml logs icecc-worker-2

# 查看构建客户端日志
docker-compose -f docker-compose-demo.yml logs build-client
```

## 技术特性

- **CMake 2.8.12兼容** - 支持较老的CMake版本
- **C++11标准** - 使用现代C++特性
- **多模块设计** - 提供足够的编译任务
- **性能测试** - 包含计算密集型任务
- **环境验证** - 自动检测分布式构建环境

## 扩展使用

可以通过以下方式扩展测试：

1. **增加源文件** - 添加更多.cpp文件以增加编译任务
2. **调整编译选项** - 修改CMakeLists.txt中的编译参数
3. **性能基准测试** - 比较单机vs分布式构建时间
4. **真实项目测试** - 将配置应用到实际项目中

## 注意事项

- 本项目主要用于验证分布式构建功能
- 在生产环境中使用前请进行充分测试
- 确保所有节点的编译器版本一致
- 注意网络安全和访问控制
